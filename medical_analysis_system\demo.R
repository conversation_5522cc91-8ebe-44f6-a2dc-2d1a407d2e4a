# 医学数据分析系统 - 演示脚本
# Medical Data Analysis System - Demo Script

cat("=================================================\n")
cat("医学数据分析系统演示\n")
cat("Medical Data Analysis System Demo\n")
cat("=================================================\n\n")

# 检查工作目录
if (!file.exists("global.R")) {
  if (file.exists("medical_analysis_system/global.R")) {
    setwd("medical_analysis_system")
  } else {
    stop("请在medical_analysis_system目录中运行此脚本")
  }
}

# 加载必要的包
required_packages <- c("shiny", "shinydashboard", "DT", "ggplot2", "dplyr")
missing_packages <- c()

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    missing_packages <- c(missing_packages, pkg)
  }
}

if (length(missing_packages) > 0) {
  cat("正在安装缺少的包...\n")
  for (pkg in missing_packages) {
    install.packages(pkg, dependencies = TRUE)
  }
}

# 加载包
suppressPackageStartupMessages({
  library(shiny)
  library(shinydashboard)
  library(DT)
  library(ggplot2)
  library(dplyr)
})

cat("✓ 必要的包已加载\n\n")

# 生成演示数据
cat("生成演示数据...\n")

set.seed(123)
n <- 500

demo_data <- data.frame(
  patient_id = 1:n,
  age = round(rnorm(n, 65, 15)),
  gender = sample(c("Male", "Female"), n, replace = TRUE, prob = c(0.55, 0.45)),
  creatinine = round(rnorm(n, 1.2, 0.4), 2),
  bun = round(rnorm(n, 20, 8), 1),
  wbc = round(rnorm(n, 8.5, 3.2), 1),
  heart_rate = round(rnorm(n, 85, 20)),
  systolic_bp = round(rnorm(n, 130, 25)),
  sofa_score = round(rnorm(n, 6, 3)),
  apache_score = round(rnorm(n, 15, 8)),
  diabetes = sample(c(0, 1), n, replace = TRUE, prob = c(0.7, 0.3)),
  hypertension = sample(c(0, 1), n, replace = TRUE, prob = c(0.6, 0.4)),
  los_icu = round(rexp(n, 0.2)),
  stringsAsFactors = FALSE
)

# 清理数据
demo_data$age[demo_data$age < 18] <- 18
demo_data$age[demo_data$age > 100] <- 100
demo_data$creatinine[demo_data$creatinine < 0.5] <- 0.5
demo_data$heart_rate[demo_data$heart_rate < 40] <- 40
demo_data$heart_rate[demo_data$heart_rate > 180] <- 180
demo_data$sofa_score[demo_data$sofa_score < 0] <- 0
demo_data$apache_score[demo_data$apache_score < 0] <- 0
demo_data$los_icu[demo_data$los_icu < 1] <- 1

# 生成结局变量
death_prob <- plogis(-2.5 + 
                    0.05 * (demo_data$age - 65) + 
                    0.3 * (demo_data$gender == "Male") +
                    0.2 * demo_data$sofa_score +
                    0.5 * demo_data$diabetes +
                    0.1 * (demo_data$creatinine - 1.2))

demo_data$death_28d <- rbinom(n, 1, death_prob)

# 添加一些缺失值
missing_indices <- sample(1:n, n * 0.05)
demo_data$creatinine[sample(missing_indices, length(missing_indices) * 0.3)] <- NA
demo_data$bun[sample(missing_indices, length(missing_indices) * 0.2)] <- NA

cat("✓ 演示数据生成完成 (", nrow(demo_data), "行,", ncol(demo_data), "列)\n\n")

# 保存演示数据
if (!dir.exists("data")) dir.create("data")
write.csv(demo_data, "data/demo_data.csv", row.names = FALSE)
cat("✓ 演示数据已保存到 data/demo_data.csv\n\n")

# 创建简化的演示应用
cat("创建演示应用...\n")

# 简化的UI
demo_ui <- dashboardPage(
  skin = "blue",
  
  dashboardHeader(
    title = "医学数据分析系统演示"
  ),
  
  dashboardSidebar(
    sidebarMenu(
      menuItem("数据概览", tabName = "overview", icon = icon("table")),
      menuItem("统计分析", tabName = "analysis", icon = icon("chart-line")),
      menuItem("可视化", tabName = "plots", icon = icon("chart-bar"))
    )
  ),
  
  dashboardBody(
    tabItems(
      # 数据概览
      tabItem(
        tabName = "overview",
        fluidRow(
          column(3, valueBoxOutput("total_patients")),
          column(3, valueBoxOutput("avg_age")),
          column(3, valueBoxOutput("death_rate")),
          column(3, valueBoxOutput("missing_rate"))
        ),
        fluidRow(
          box(
            title = "数据预览",
            status = "primary",
            solidHeader = TRUE,
            width = 12,
            DT::dataTableOutput("data_preview")
          )
        )
      ),
      
      # 统计分析
      tabItem(
        tabName = "analysis",
        fluidRow(
          column(4,
            box(
              title = "分析设置",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              selectInput("outcome_var", "结局变量", 
                         choices = c("28天死亡率" = "death_28d")),
              checkboxGroupInput("covariates", "协变量",
                               choices = c("年龄" = "age", "性别" = "gender", 
                                         "肌酐" = "creatinine", "SOFA评分" = "sofa_score",
                                         "糖尿病" = "diabetes", "高血压" = "hypertension")),
              actionButton("run_analysis", "开始分析", class = "btn-success")
            )
          ),
          column(8,
            box(
              title = "分析结果",
              status = "success",
              solidHeader = TRUE,
              width = NULL,
              DT::dataTableOutput("analysis_results")
            )
          )
        )
      ),
      
      # 可视化
      tabItem(
        tabName = "plots",
        fluidRow(
          column(6,
            box(
              title = "年龄分布",
              status = "info",
              solidHeader = TRUE,
              width = NULL,
              plotOutput("age_plot")
            )
          ),
          column(6,
            box(
              title = "生存状态比较",
              status = "warning",
              solidHeader = TRUE,
              width = NULL,
              plotOutput("survival_plot")
            )
          )
        ),
        fluidRow(
          column(12,
            box(
              title = "相关性分析",
              status = "success",
              solidHeader = TRUE,
              width = NULL,
              plotOutput("correlation_plot")
            )
          )
        )
      )
    )
  )
)

# 简化的服务器逻辑
demo_server <- function(input, output, session) {
  
  # 值框输出
  output$total_patients <- renderValueBox({
    valueBox(
      value = nrow(demo_data),
      subtitle = "总患者数",
      icon = icon("users"),
      color = "blue"
    )
  })
  
  output$avg_age <- renderValueBox({
    valueBox(
      value = round(mean(demo_data$age, na.rm = TRUE), 1),
      subtitle = "平均年龄",
      icon = icon("birthday-cake"),
      color = "green"
    )
  })
  
  output$death_rate <- renderValueBox({
    valueBox(
      value = paste0(round(mean(demo_data$death_28d) * 100, 1), "%"),
      subtitle = "28天死亡率",
      icon = icon("heartbeat"),
      color = "red"
    )
  })
  
  output$missing_rate <- renderValueBox({
    missing_rate <- sum(is.na(demo_data)) / (nrow(demo_data) * ncol(demo_data)) * 100
    valueBox(
      value = paste0(round(missing_rate, 1), "%"),
      subtitle = "缺失值比例",
      icon = icon("question-circle"),
      color = "yellow"
    )
  })
  
  # 数据预览
  output$data_preview <- DT::renderDataTable({
    DT::datatable(
      demo_data,
      options = list(scrollX = TRUE, pageLength = 10),
      class = "display nowrap compact",
      rownames = FALSE
    )
  })
  
  # 分析结果
  analysis_results <- eventReactive(input$run_analysis, {
    req(input$covariates)
    
    # 简单的单因素分析
    results <- data.frame(
      Variable = character(),
      OR = numeric(),
      CI_Lower = numeric(),
      CI_Upper = numeric(),
      P_Value = numeric(),
      stringsAsFactors = FALSE
    )
    
    for (var in input$covariates) {
      tryCatch({
        if (var %in% c("gender", "diabetes", "hypertension")) {
          # 分类变量
          formula_str <- paste("death_28d ~", var)
        } else {
          # 连续变量
          formula_str <- paste("death_28d ~", var)
        }
        
        model <- glm(as.formula(formula_str), data = demo_data, family = binomial())
        coef_summary <- summary(model)$coefficients
        
        if (nrow(coef_summary) > 1) {
          or <- exp(coef_summary[2, 1])
          se <- coef_summary[2, 2]
          ci_lower <- exp(coef_summary[2, 1] - 1.96 * se)
          ci_upper <- exp(coef_summary[2, 1] + 1.96 * se)
          p_value <- coef_summary[2, 4]
          
          results <- rbind(results, data.frame(
            Variable = var,
            OR = round(or, 3),
            CI_Lower = round(ci_lower, 3),
            CI_Upper = round(ci_upper, 3),
            P_Value = round(p_value, 4)
          ))
        }
      }, error = function(e) {
        # 忽略错误
      })
    }
    
    return(results)
  })
  
  output$analysis_results <- DT::renderDataTable({
    DT::datatable(
      analysis_results(),
      options = list(pageLength = 10),
      rownames = FALSE
    )
  })
  
  # 图表
  output$age_plot <- renderPlot({
    ggplot(demo_data, aes(x = age, fill = factor(death_28d))) +
      geom_histogram(bins = 30, alpha = 0.7, position = "identity") +
      scale_fill_manual(values = c("0" = "lightblue", "1" = "red"),
                       labels = c("存活", "死亡")) +
      labs(title = "年龄分布", x = "年龄", y = "频数", fill = "结局") +
      theme_minimal()
  })
  
  output$survival_plot <- renderPlot({
    survival_summary <- demo_data %>%
      group_by(death_28d) %>%
      summarise(
        count = n(),
        avg_age = mean(age, na.rm = TRUE),
        avg_sofa = mean(sofa_score, na.rm = TRUE),
        .groups = 'drop'
      )
    
    ggplot(survival_summary, aes(x = factor(death_28d), y = count, fill = factor(death_28d))) +
      geom_bar(stat = "identity") +
      scale_fill_manual(values = c("0" = "lightblue", "1" = "red")) +
      labs(title = "生存状态分布", x = "28天死亡", y = "患者数", fill = "结局") +
      scale_x_discrete(labels = c("0" = "存活", "1" = "死亡")) +
      theme_minimal() +
      theme(legend.position = "none")
  })
  
  output$correlation_plot <- renderPlot({
    numeric_vars <- demo_data[, sapply(demo_data, is.numeric)]
    cor_matrix <- cor(numeric_vars, use = "complete.obs")
    
    # 转换为长格式
    cor_melted <- expand.grid(Var1 = rownames(cor_matrix), Var2 = colnames(cor_matrix))
    cor_melted$value <- as.vector(cor_matrix)
    
    ggplot(cor_melted, aes(Var1, Var2, fill = value)) +
      geom_tile() +
      scale_fill_gradient2(low = "blue", high = "red", mid = "white", 
                          midpoint = 0, limit = c(-1, 1)) +
      theme_minimal() +
      theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
      labs(title = "变量相关性热图", x = "", y = "", fill = "相关系数")
  })
}

cat("✓ 演示应用创建完成\n\n")

# 启动演示应用
cat("启动演示应用...\n")
cat("请在浏览器中查看演示效果\n")
cat("按 Ctrl+C 停止应用\n\n")

cat("演示功能:\n")
cat("1. 数据概览 - 查看演示数据的基本信息\n")
cat("2. 统计分析 - 进行简单的单因素分析\n")
cat("3. 可视化 - 查看数据分布和相关性图表\n\n")

# 运行应用
shinyApp(ui = demo_ui, server = demo_server)
