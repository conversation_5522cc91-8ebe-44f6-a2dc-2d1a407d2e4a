# 医学数据分析系统 - 主应用入口
# Medical Data Analysis System - Main Application Entry

# 加载全局配置
source("global.R")

# 加载UI模块
source("ui/ui_main.R")

# 加载服务器模块
source("server/server_main.R")

# 加载工具函数
source("utils/helpers.R")
source("utils/validators.R")
source("utils/config.R")

# 加载核心分析模块
source("modules/data_processing.R")
source("modules/statistical_analysis.R")
source("modules/visualization.R")
source("modules/model_building.R")

# 创建Shiny应用
shinyApp(
  ui = ui,
  server = server,
  options = list(
    host = "0.0.0.0",
    port = 3838,
    launch.browser = TRUE
  )
)
