# 医学数据分析系统 - 启动脚本
# Medical Data Analysis System - Launch Script

# 设置工作目录
if (!exists("app_root")) {
  app_root <- getwd()
  if (basename(app_root) != "medical_analysis_system") {
    if (dir.exists("medical_analysis_system")) {
      setwd("medical_analysis_system")
      app_root <- getwd()
    } else {
      stop("请在medical_analysis_system目录中运行此脚本")
    }
  }
}

cat("医学数据分析系统启动中...\n")
cat("Medical Data Analysis System Starting...\n\n")

# 检查R版本
r_version <- R.Version()
cat("R版本:", r_version$version.string, "\n")

if (as.numeric(paste0(r_version$major, ".", r_version$minor)) < 4.0) {
  warning("建议使用R 4.0或更高版本以获得最佳体验")
}

# 检查必要的包
required_packages <- c(
  "shiny", "shinydashboard", "shinyWidgets", "DT", "plotly",
  "dplyr", "ggplot2", "rms", "survival", "pROC"
)

missing_packages <- c()
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    missing_packages <- c(missing_packages, pkg)
  }
}

if (length(missing_packages) > 0) {
  cat("缺少以下必要的R包:\n")
  cat("Missing required packages:\n")
  for (pkg in missing_packages) {
    cat("  -", pkg, "\n")
  }
  cat("\n请运行以下命令安装缺少的包:\n")
  cat("Please run the following command to install missing packages:\n")
  cat("source('install_packages.R')\n\n")
  
  # 询问是否自动安装
  if (interactive()) {
    response <- readline("是否现在自动安装缺少的包? (y/n): ")
    if (tolower(response) %in% c("y", "yes", "是")) {
      cat("正在安装缺少的包...\n")
      for (pkg in missing_packages) {
        tryCatch({
          install.packages(pkg, dependencies = TRUE)
          cat("✓ 已安装:", pkg, "\n")
        }, error = function(e) {
          cat("✗ 安装失败:", pkg, "-", e$message, "\n")
        })
      }
    }
  } else {
    stop("请先安装缺少的包")
  }
}

# 加载必要的包
cat("加载系统组件...\n")
cat("Loading system components...\n")

tryCatch({
  # 检查文件结构
  required_files <- c(
    "global.R",
    "app.R", 
    "ui/ui_main.R",
    "server/server_main.R",
    "modules/data_processing.R",
    "utils/helpers.R"
  )
  
  missing_files <- c()
  for (file in required_files) {
    if (!file.exists(file)) {
      missing_files <- c(missing_files, file)
    }
  }
  
  if (length(missing_files) > 0) {
    cat("缺少以下系统文件:\n")
    cat("Missing system files:\n")
    for (file in missing_files) {
      cat("  -", file, "\n")
    }
    stop("系统文件不完整，请检查安装")
  }
  
  # 创建必要的目录
  required_dirs <- c("data", "reports", "logs", "temp", "exports", "www/css", "www/js")
  for (dir in required_dirs) {
    if (!dir.exists(dir)) {
      dir.create(dir, recursive = TRUE)
      cat("创建目录:", dir, "\n")
    }
  }
  
  # 检查并创建示例数据
  if (!file.exists("data/sample_data.csv")) {
    cat("生成示例数据...\n")
    source("utils/helpers.R")
    sample_data <- generate_sample_data(500)
    write.csv(sample_data, "data/sample_data.csv", row.names = FALSE)
    cat("✓ 示例数据已生成: data/sample_data.csv\n")
  }
  
  cat("✓ 系统组件检查完成\n\n")
  
}, error = function(e) {
  cat("✗ 系统组件检查失败:", e$message, "\n")
  stop("系统初始化失败")
})

# 启动应用
cat("启动Web应用...\n")
cat("Starting web application...\n\n")

cat("系统信息:\n")
cat("System Information:\n")
cat("- 应用名称: 医学数据分析系统\n")
cat("- Application: Medical Data Analysis System\n")
cat("- 版本: 1.0.0\n")
cat("- Version: 1.0.0\n")
cat("- 工作目录:", getwd(), "\n")
cat("- Working Directory:", getwd(), "\n")
cat("- 访问地址: http://localhost:3838\n")
cat("- Access URL: http://localhost:3838\n\n")

cat("功能特色:\n")
cat("Features:\n")
cat("✓ 自动化数据预处理和清洗\n")
cat("✓ 完整的统计分析流程\n") 
cat("✓ 专业的医学统计图表\n")
cat("✓ 一键生成分析报告\n")
cat("✓ 交互式结果展示\n\n")

cat("使用说明:\n")
cat("Usage Instructions:\n")
cat("1. 在浏览器中访问 http://localhost:3838\n")
cat("2. 上传您的数据文件 (支持CSV, Excel等格式)\n")
cat("3. 配置分析参数\n")
cat("4. 执行统计分析\n")
cat("5. 查看结果和生成报告\n\n")

cat("注意事项:\n")
cat("Notes:\n")
cat("- 最大文件大小: 100MB\n")
cat("- 支持格式: CSV, TSV, Excel (.xlsx, .xls)\n")
cat("- 建议样本量: ≥30\n")
cat("- 按Ctrl+C停止应用\n\n")

# 启动Shiny应用
tryCatch({
  # 设置选项
  options(
    shiny.host = "0.0.0.0",
    shiny.port = 3838,
    shiny.launch.browser = TRUE,
    shiny.maxRequestSize = 100*1024^2  # 100MB
  )
  
  # 运行应用
  shiny::runApp(
    appDir = ".",
    host = "0.0.0.0",
    port = 3838,
    launch.browser = TRUE
  )
  
}, error = function(e) {
  cat("应用启动失败:", e$message, "\n")
  cat("Application failed to start:", e$message, "\n")
  
  # 提供故障排除建议
  cat("\n故障排除建议:\n")
  cat("Troubleshooting suggestions:\n")
  cat("1. 检查端口3838是否被占用\n")
  cat("2. 确保所有必要的R包已安装\n")
  cat("3. 检查系统文件是否完整\n")
  cat("4. 查看错误日志: logs/app.log\n")
  
  # 尝试使用其他端口
  if (interactive()) {
    response <- readline("是否尝试使用其他端口? (y/n): ")
    if (tolower(response) %in% c("y", "yes", "是")) {
      alternative_ports <- c(8080, 8888, 9999, 4444)
      for (port in alternative_ports) {
        cat("尝试端口", port, "...\n")
        tryCatch({
          shiny::runApp(
            appDir = ".",
            host = "0.0.0.0", 
            port = port,
            launch.browser = TRUE
          )
          break
        }, error = function(e2) {
          cat("端口", port, "也失败了\n")
        })
      }
    }
  }
}, finally = {
  cat("\n应用已停止\n")
  cat("Application stopped\n")
})
