# 医学数据分析系统 - 配置管理
# Medical Data Analysis System - Configuration Management

# 检查并加载R6包
if (!requireNamespace("R6", quietly = TRUE)) {
  install.packages("R6")
}
library(R6)

# 系统配置类
SystemConfig <- R6::R6Class("SystemConfig",
  public = list(
    # 配置存储
    config = NULL,
    
    # 初始化
    initialize = function(config_file = NULL) {
      self$load_default_config()
      if (!is.null(config_file) && file.exists(config_file)) {
        self$load_config_file(config_file)
      }
    },
    
    # 加载默认配置
    load_default_config = function() {
      self$config <- list(
        # 应用基本信息
        app = list(
          name = "医学数据分析系统",
          version = "1.0.0",
          author = "Medical Data Analysis Team",
          description = "基于Shiny的医学数据统计分析和预测建模系统"
        ),
        
        # 文件处理配置
        file = list(
          max_size_mb = 100,
          supported_types = c(".csv", ".tsv", ".txt", ".xlsx", ".xls"),
          encoding_options = c("UTF-8", "GBK", "GB2312", "latin1"),
          separator_options = list(
            "逗号 (,)" = ",",
            "制表符" = "\t", 
            "分号 (;)" = ";",
            "空格" = " "
          )
        ),
        
        # 数据处理配置
        data_processing = list(
          missing_methods = list(
            "删除含缺失值的行" = "remove",
            "均值填充" = "mean",
            "中位数填充" = "median", 
            "MICE多重插补" = "mice",
            "保持原样" = "none"
          ),
          mice_default_iterations = 5,
          outlier_threshold_default = 1.5,
          min_sample_size = 30,
          max_missing_rate = 0.5
        ),
        
        # 统计分析配置
        analysis = list(
          default_alpha = 0.05,
          lasso_alpha_default = 1,
          cv_folds_default = 5,
          min_events_per_variable = 10,
          max_variables_ratio = 0.1,
          adjustment_methods = list(
            "Bonferroni" = "bonferroni",
            "Benjamini-Hochberg" = "BH",
            "Benjamini-Yekutieli" = "BY"
          )
        ),
        
        # 可视化配置
        visualization = list(
          default_width = 800,
          default_height = 600,
          default_dpi = 300,
          color_palettes = list(
            primary = c("#667eea", "#764ba2"),
            success = c("#56ab2f", "#a8e6cf"),
            warning = c("#f093fb", "#f5576c"),
            info = c("#4facfe", "#00f2fe"),
            danger = c("#fa709a", "#fee140")
          ),
          plot_formats = c("png", "pdf", "svg", "jpeg")
        ),
        
        # 报告配置
        reports = list(
          output_formats = list(
            "HTML" = "html",
            "PDF" = "pdf", 
            "Word" = "docx"
          ),
          templates = list(
            "标准模板" = "standard",
            "简洁模板" = "minimal",
            "详细模板" = "detailed",
            "学术模板" = "academic"
          ),
          max_title_length = 200,
          default_author = "数据分析师"
        ),
        
        # 性能配置
        performance = list(
          max_rows_preview = 1000,
          max_plot_points = 10000,
          cache_enabled = TRUE,
          parallel_processing = FALSE,
          max_cores = 2
        ),
        
        # 安全配置
        security = list(
          max_upload_size = 100 * 1024^2,  # 100MB
          allowed_file_types = c("csv", "tsv", "txt", "xlsx", "xls"),
          sanitize_column_names = TRUE,
          validate_data_types = TRUE
        ),
        
        # 日志配置
        logging = list(
          level = "INFO",
          file_path = "logs/app.log",
          max_file_size = "10MB",
          max_files = 5,
          console_output = TRUE
        ),
        
        # UI配置
        ui = list(
          theme = "flatly",
          sidebar_width = 280,
          main_colors = list(
            primary = "#3498db",
            success = "#2ecc71", 
            warning = "#f39c12",
            danger = "#e74c3c",
            info = "#17a2b8"
          ),
          animation_enabled = TRUE,
          responsive_design = TRUE
        )
      )
    },
    
    # 从文件加载配置
    load_config_file = function(config_file) {
      tryCatch({
        if (tools::file_ext(config_file) == "json") {
          file_config <- jsonlite::fromJSON(config_file)
        } else if (tools::file_ext(config_file) %in% c("yml", "yaml")) {
          file_config <- yaml::read_yaml(config_file)
        } else {
          stop("不支持的配置文件格式")
        }
        
        # 合并配置
        self$config <- modifyList(self$config, file_config)
        log_info(paste("成功加载配置文件:", config_file))
        
      }, error = function(e) {
        log_error(paste("加载配置文件失败:", e$message))
      })
    },
    
    # 保存配置到文件
    save_config_file = function(config_file, format = "json") {
      tryCatch({
        if (format == "json") {
          jsonlite::write_json(self$config, config_file, pretty = TRUE, auto_unbox = TRUE)
        } else if (format == "yaml") {
          yaml::write_yaml(self$config, config_file)
        } else {
          stop("不支持的配置文件格式")
        }
        
        log_info(paste("配置已保存到:", config_file))
        
      }, error = function(e) {
        log_error(paste("保存配置文件失败:", e$message))
      })
    },
    
    # 获取配置值
    get = function(key_path, default = NULL) {
      keys <- strsplit(key_path, "\\.")[[1]]
      value <- self$config
      
      for (key in keys) {
        if (is.list(value) && key %in% names(value)) {
          value <- value[[key]]
        } else {
          return(default)
        }
      }
      
      return(value)
    },
    
    # 设置配置值
    set = function(key_path, value) {
      keys <- strsplit(key_path, "\\.")[[1]]
      config_ref <- self$config
      
      # 导航到父级
      for (i in 1:(length(keys) - 1)) {
        key <- keys[i]
        if (!is.list(config_ref[[key]])) {
          config_ref[[key]] <- list()
        }
        config_ref <- config_ref[[key]]
      }
      
      # 设置值
      final_key <- keys[length(keys)]
      config_ref[[final_key]] <- value
      
      log_info(paste("配置已更新:", key_path))
    },
    
    # 验证配置
    validate_config = function() {
      errors <- c()
      warnings <- c()
      
      # 验证文件大小限制
      max_size <- self$get("file.max_size_mb")
      if (is.null(max_size) || max_size <= 0 || max_size > 1000) {
        errors <- c(errors, "文件大小限制配置无效")
      }
      
      # 验证支持的文件类型
      supported_types <- self$get("file.supported_types")
      if (is.null(supported_types) || length(supported_types) == 0) {
        errors <- c(errors, "未配置支持的文件类型")
      }
      
      # 验证显著性水平
      alpha <- self$get("analysis.default_alpha")
      if (is.null(alpha) || alpha <= 0 || alpha >= 1) {
        errors <- c(errors, "默认显著性水平配置无效")
      }
      
      # 验证可视化配置
      width <- self$get("visualization.default_width")
      height <- self$get("visualization.default_height")
      if (is.null(width) || width < 100 || width > 2000) {
        warnings <- c(warnings, "默认图表宽度可能不合适")
      }
      if (is.null(height) || height < 100 || height > 2000) {
        warnings <- c(warnings, "默认图表高度可能不合适")
      }
      
      return(list(
        valid = length(errors) == 0,
        errors = errors,
        warnings = warnings
      ))
    },
    
    # 重置为默认配置
    reset_to_default = function() {
      self$load_default_config()
      log_info("配置已重置为默认值")
    },
    
    # 获取所有配置
    get_all = function() {
      return(self$config)
    },
    
    # 打印配置摘要
    print_summary = function() {
      cat("医学数据分析系统配置摘要\n")
      cat("========================\n")
      cat("应用名称:", self$get("app.name"), "\n")
      cat("版本:", self$get("app.version"), "\n")
      cat("最大文件大小:", self$get("file.max_size_mb"), "MB\n")
      cat("支持文件类型:", paste(self$get("file.supported_types"), collapse = ", "), "\n")
      cat("默认显著性水平:", self$get("analysis.default_alpha"), "\n")
      cat("默认图表尺寸:", self$get("visualization.default_width"), "x", 
          self$get("visualization.default_height"), "\n")
      cat("日志级别:", self$get("logging.level"), "\n")
      cat("========================\n")
    }
  )
)

# 创建全局配置实例
if (!exists("app_config")) {
  app_config <- SystemConfig$new()
}

# 配置相关的辅助函数
get_config <- function(key_path, default = NULL) {
  return(app_config$get(key_path, default))
}

set_config <- function(key_path, value) {
  app_config$set(key_path, value)
}

validate_system_config <- function() {
  return(app_config$validate_config())
}

# 加载用户自定义配置（如果存在）
load_user_config <- function() {
  user_config_file <- "config/user_config.json"
  if (file.exists(user_config_file)) {
    app_config$load_config_file(user_config_file)
    log_info("已加载用户自定义配置")
  }
}

# 保存当前配置
save_current_config <- function(file_path = "config/current_config.json") {
  dir.create(dirname(file_path), showWarnings = FALSE, recursive = TRUE)
  app_config$save_config_file(file_path)
}

# 系统启动时加载配置
initialize_config <- function() {
  # 创建配置目录
  dir.create("config", showWarnings = FALSE)
  
  # 加载用户配置
  load_user_config()
  
  # 验证配置
  validation <- validate_system_config()
  if (!validation$valid) {
    log_error("配置验证失败:")
    for (error in validation$errors) {
      log_error(paste("  -", error))
    }
  }
  
  if (length(validation$warnings) > 0) {
    log_warn("配置警告:")
    for (warning in validation$warnings) {
      log_warn(paste("  -", warning))
    }
  }
  
  # 打印配置摘要
  app_config$print_summary()
}

# 在应用启动时初始化配置
initialize_config()
