# 医学数据分析系统 - 数据处理模块
# Medical Data Analysis System - Data Processing Module

# 数据读取函数
read_medical_data <- function(file_path, separator = ",", encoding = "UTF-8", 
                             header = TRUE, stringsAsFactors = FALSE) {
  tryCatch({
    # 根据文件扩展名选择读取方法
    file_ext <- tools::file_ext(file_path)
    
    if (file_ext %in% c("csv", "txt", "tsv")) {
      data <- read.csv(
        file_path,
        sep = separator,
        header = header,
        stringsAsFactors = stringsAsFactors,
        fileEncoding = encoding,
        na.strings = c("", "NA", "NULL", "null", "N/A", "#N/A")
      )
    } else if (file_ext %in% c("xlsx", "xls")) {
      library(readxl)
      data <- read_excel(file_path, na = c("", "NA", "NULL", "null", "N/A", "#N/A"))
    } else {
      stop("不支持的文件格式")
    }
    
    # 基本数据验证
    if (nrow(data) == 0) {
      stop("数据文件为空")
    }
    
    if (ncol(data) < 2) {
      stop("数据列数不足，至少需要2列")
    }
    
    log_info(paste("成功读取数据:", nrow(data), "行,", ncol(data), "列"))
    return(data)
    
  }, error = function(e) {
    log_error(paste("数据读取失败:", e$message))
    stop(paste("数据读取失败:", e$message))
  })
}

# 数据质量检查函数
check_data_quality <- function(data) {
  tryCatch({
    quality_report <- list()
    
    # 基本信息
    quality_report$basic_info <- list(
      rows = nrow(data),
      cols = ncol(data),
      size_mb = round(object.size(data) / 1024^2, 2)
    )
    
    # 变量类型统计
    var_types <- sapply(data, class)
    quality_report$variable_types <- list(
      numeric = sum(sapply(data, is.numeric)),
      character = sum(sapply(data, is.character)),
      factor = sum(sapply(data, is.factor)),
      logical = sum(sapply(data, is.logical)),
      date = sum(sapply(data, function(x) inherits(x, "Date")))
    )
    
    # 缺失值分析
    missing_counts <- sapply(data, function(x) sum(is.na(x)))
    missing_percentages <- round(missing_counts / nrow(data) * 100, 2)
    
    quality_report$missing_values <- list(
      total_missing = sum(missing_counts),
      missing_percentage = round(sum(missing_counts) / (nrow(data) * ncol(data)) * 100, 2),
      variables_with_missing = sum(missing_counts > 0),
      missing_by_variable = data.frame(
        variable = names(missing_counts),
        missing_count = missing_counts,
        missing_percentage = missing_percentages,
        stringsAsFactors = FALSE
      )
    )
    
    # 重复行检查
    quality_report$duplicates <- list(
      duplicate_rows = sum(duplicated(data)),
      unique_rows = nrow(unique(data))
    )
    
    # 数值变量统计
    numeric_vars <- data[sapply(data, is.numeric)]
    if (ncol(numeric_vars) > 0) {
      quality_report$numeric_summary <- summary(numeric_vars)
      
      # 异常值检测（使用IQR方法）
      outliers_info <- list()
      for (var in names(numeric_vars)) {
        if (sum(!is.na(numeric_vars[[var]])) > 0) {
          Q1 <- quantile(numeric_vars[[var]], 0.25, na.rm = TRUE)
          Q3 <- quantile(numeric_vars[[var]], 0.75, na.rm = TRUE)
          IQR <- Q3 - Q1
          lower_bound <- Q1 - 1.5 * IQR
          upper_bound <- Q3 + 1.5 * IQR
          
          outliers <- sum(numeric_vars[[var]] < lower_bound | 
                         numeric_vars[[var]] > upper_bound, na.rm = TRUE)
          outliers_info[[var]] <- outliers
        }
      }
      quality_report$outliers <- outliers_info
    }
    
    log_info("数据质量检查完成")
    return(quality_report)
    
  }, error = function(e) {
    log_error(paste("数据质量检查失败:", e$message))
    stop(paste("数据质量检查失败:", e$message))
  })
}

# 缺失值处理函数
handle_missing_values <- function(data, method = "mice", mice_iterations = 5) {
  tryCatch({
    original_rows <- nrow(data)
    
    if (method == "remove") {
      # 删除含缺失值的行
      cleaned_data <- na.omit(data)
      log_info(paste("删除", original_rows - nrow(cleaned_data), "行含缺失值的数据"))
      
    } else if (method == "mean") {
      # 均值填充
      cleaned_data <- data
      numeric_cols <- sapply(data, is.numeric)
      
      for (col in names(data)[numeric_cols]) {
        if (sum(is.na(data[[col]])) > 0) {
          mean_val <- mean(data[[col]], na.rm = TRUE)
          cleaned_data[[col]][is.na(cleaned_data[[col]])] <- mean_val
        }
      }
      log_info("使用均值填充数值变量的缺失值")
      
    } else if (method == "median") {
      # 中位数填充
      cleaned_data <- data
      numeric_cols <- sapply(data, is.numeric)
      
      for (col in names(data)[numeric_cols]) {
        if (sum(is.na(data[[col]])) > 0) {
          median_val <- median(data[[col]], na.rm = TRUE)
          cleaned_data[[col]][is.na(cleaned_data[[col]])] <- median_val
        }
      }
      log_info("使用中位数填充数值变量的缺失值")
      
    } else if (method == "mice") {
      # MICE多重插补
      library(mice)
      library(VIM)
      
      # 检查缺失值模式
      missing_pattern <- md.pattern(data, plot = FALSE)
      
      # 找出缺失值比例超过20%的变量
      missing_percentages <- sapply(data, function(x) sum(is.na(x)) / length(x))
      high_missing_vars <- names(missing_percentages)[missing_percentages > 0.2]
      
      if (length(high_missing_vars) > 0) {
        log_warn(paste("以下变量缺失值超过20%，将被排除:", paste(high_missing_vars, collapse = ", ")))
        data_for_mice <- data[, !names(data) %in% high_missing_vars]
      } else {
        data_for_mice <- data
      }
      
      # 只对有缺失值的变量进行MICE
      missing_vars <- names(data_for_mice)[sapply(data_for_mice, function(x) sum(is.na(x)) > 0)]
      complete_vars <- names(data_for_mice)[sapply(data_for_mice, function(x) sum(is.na(x)) == 0)]
      
      if (length(missing_vars) > 0) {
        mice_result <- mice(
          data_for_mice[, missing_vars, drop = FALSE],
          method = "pmm",
          maxit = mice_iterations,
          seed = DEFAULT_SEED,
          printFlag = FALSE
        )
        
        # 获取完整数据
        imputed_data <- complete(mice_result, 1)
        
        # 合并完整变量和插补变量
        cleaned_data <- cbind(data_for_mice[, complete_vars, drop = FALSE], imputed_data)
        
        # 重新排列列顺序
        cleaned_data <- cleaned_data[, names(data_for_mice)]
        
        log_info(paste("使用MICE方法插补", length(missing_vars), "个变量的缺失值"))
      } else {
        cleaned_data <- data_for_mice
        log_info("数据无缺失值，无需插补")
      }
      
    } else {
      # 保持原样
      cleaned_data <- data
      log_info("保持原始数据，未处理缺失值")
    }
    
    return(cleaned_data)
    
  }, error = function(e) {
    log_error(paste("缺失值处理失败:", e$message))
    stop(paste("缺失值处理失败:", e$message))
  })
}

# 异常值处理函数
remove_outliers <- function(data, threshold = 1.5) {
  tryCatch({
    cleaned_data <- data
    numeric_cols <- sapply(data, is.numeric)
    outliers_removed <- 0
    
    for (col in names(data)[numeric_cols]) {
      if (sum(!is.na(data[[col]])) > 0) {
        Q1 <- quantile(data[[col]], 0.25, na.rm = TRUE)
        Q3 <- quantile(data[[col]], 0.75, na.rm = TRUE)
        IQR <- Q3 - Q1
        lower_bound <- Q1 - threshold * IQR
        upper_bound <- Q3 + threshold * IQR
        
        outliers <- which(data[[col]] < lower_bound | data[[col]] > upper_bound)
        if (length(outliers) > 0) {
          cleaned_data <- cleaned_data[-outliers, ]
          outliers_removed <- outliers_removed + length(outliers)
        }
      }
    }
    
    log_info(paste("移除", outliers_removed, "个异常值"))
    return(cleaned_data)
    
  }, error = function(e) {
    log_error(paste("异常值处理失败:", e$message))
    stop(paste("异常值处理失败:", e$message))
  })
}

# 数据标准化函数
normalize_data <- function(data, method = "z_score") {
  tryCatch({
    normalized_data <- data
    numeric_cols <- sapply(data, is.numeric)
    
    for (col in names(data)[numeric_cols]) {
      if (sum(!is.na(data[[col]])) > 0) {
        if (method == "z_score") {
          # Z-score标准化
          mean_val <- mean(data[[col]], na.rm = TRUE)
          sd_val <- sd(data[[col]], na.rm = TRUE)
          if (sd_val > 0) {
            normalized_data[[col]] <- (data[[col]] - mean_val) / sd_val
          }
        } else if (method == "min_max") {
          # Min-Max标准化
          min_val <- min(data[[col]], na.rm = TRUE)
          max_val <- max(data[[col]], na.rm = TRUE)
          if (max_val > min_val) {
            normalized_data[[col]] <- (data[[col]] - min_val) / (max_val - min_val)
          }
        }
      }
    }
    
    log_info(paste("使用", method, "方法标准化数值变量"))
    return(normalized_data)
    
  }, error = function(e) {
    log_error(paste("数据标准化失败:", e$message))
    stop(paste("数据标准化失败:", e$message))
  })
}

# 对数变换函数
log_transform_data <- function(data) {
  tryCatch({
    transformed_data <- data
    numeric_cols <- sapply(data, is.numeric)
    
    for (col in names(data)[numeric_cols]) {
      if (sum(!is.na(data[[col]])) > 0 && all(data[[col]] > 0, na.rm = TRUE)) {
        transformed_data[[paste0(col, "_log")]] <- log(data[[col]])
      }
    }
    
    log_info("对正数变量进行对数变换")
    return(transformed_data)
    
  }, error = function(e) {
    log_error(paste("对数变换失败:", e$message))
    stop(paste("对数变换失败:", e$message))
  })
}

# 变量类型自动识别函数
auto_detect_variable_types <- function(data) {
  tryCatch({
    type_info <- list()
    
    for (col in names(data)) {
      col_data <- data[[col]]
      
      # 移除缺失值进行判断
      non_na_data <- col_data[!is.na(col_data)]
      
      if (length(non_na_data) == 0) {
        type_info[[col]] <- "unknown"
        next
      }
      
      # 判断是否为数值型
      if (is.numeric(col_data)) {
        # 进一步判断是连续型还是分类型
        unique_values <- length(unique(non_na_data))
        if (unique_values <= 10 && all(non_na_data == round(non_na_data))) {
          type_info[[col]] <- "categorical_numeric"
        } else {
          type_info[[col]] <- "continuous"
        }
      } else if (is.character(col_data) || is.factor(col_data)) {
        unique_values <- length(unique(non_na_data))
        if (unique_values <= 20) {
          type_info[[col]] <- "categorical"
        } else {
          type_info[[col]] <- "text"
        }
      } else if (inherits(col_data, "Date")) {
        type_info[[col]] <- "date"
      } else if (is.logical(col_data)) {
        type_info[[col]] <- "binary"
      } else {
        type_info[[col]] <- "other"
      }
    }
    
    log_info("完成变量类型自动识别")
    return(type_info)
    
  }, error = function(e) {
    log_error(paste("变量类型识别失败:", e$message))
    stop(paste("变量类型识别失败:", e$message))
  })
}

# 数据预处理主函数
preprocess_medical_data <- function(data, options = list()) {
  tryCatch({
    log_info("开始数据预处理")
    
    # 默认选项
    default_options <- list(
      missing_method = "mice",
      mice_iterations = 5,
      normalize = FALSE,
      log_transform = FALSE,
      remove_outliers = FALSE,
      outlier_threshold = 1.5
    )
    
    # 合并用户选项
    options <- modifyList(default_options, options)
    
    processed_data <- data
    
    # 1. 处理缺失值
    if (options$missing_method != "none") {
      processed_data <- handle_missing_values(
        processed_data, 
        method = options$missing_method,
        mice_iterations = options$mice_iterations
      )
    }
    
    # 2. 移除异常值
    if (options$remove_outliers) {
      processed_data <- remove_outliers(processed_data, options$outlier_threshold)
    }
    
    # 3. 数据标准化
    if (options$normalize) {
      processed_data <- normalize_data(processed_data, method = "z_score")
    }
    
    # 4. 对数变换
    if (options$log_transform) {
      processed_data <- log_transform_data(processed_data)
    }
    
    log_info("数据预处理完成")
    return(processed_data)
    
  }, error = function(e) {
    log_error(paste("数据预处理失败:", e$message))
    stop(paste("数据预处理失败:", e$message))
  })
}
