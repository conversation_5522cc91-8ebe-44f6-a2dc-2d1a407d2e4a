# 医学数据分析系统 - 主程序启动脚本
# Medical Data Analysis System - Main Application Launcher

cat("=================================================\n")
cat("医学数据分析系统 - 完整版本\n")
cat("Medical Data Analysis System - Full Version\n")
cat("=================================================\n\n")

# 检查工作目录
if (!file.exists("app.R")) {
  if (file.exists("medical_analysis_system/app.R")) {
    setwd("medical_analysis_system")
  } else {
    stop("请在medical_analysis_system目录中运行此脚本")
  }
}

cat("当前工作目录:", getwd(), "\n")
cat("R版本:", R.version.string, "\n\n")

# 安全加载函数
safe_require <- function(pkg) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    cat("正在安装包:", pkg, "\n")
    install.packages(pkg, dependencies = TRUE)
  }
  library(pkg, character.only = TRUE)
  return(TRUE)
}

# 检查和安装必要的包
cat("检查必要的R包...\n")
required_packages <- c(
  # Shiny核心
  "shiny", "shinydashboard", "shinyWidgets", "DT", "plotly", "shinyjs",
  
  # 数据处理
  "dplyr", "tidyr", "stringr", "readr", "readxl",
  
  # 统计分析
  "survival", "rms", "glmnet", "pROC", "broom",
  
  # 可视化
  "ggplot2", "corrplot", "RColorBrewer",
  
  # 其他工具
  "jsonlite"
)

missing_packages <- c()
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    missing_packages <- c(missing_packages, pkg)
  }
}

if (length(missing_packages) > 0) {
  cat("需要安装以下包:\n")
  for (pkg in missing_packages) {
    cat("  -", pkg, "\n")
  }
  
  if (interactive()) {
    response <- readline("是否现在安装? (y/n): ")
    if (tolower(response) %in% c("y", "yes", "是")) {
      for (pkg in missing_packages) {
        tryCatch({
          install.packages(pkg, dependencies = TRUE)
          cat("✓ 已安装:", pkg, "\n")
        }, error = function(e) {
          cat("✗ 安装失败:", pkg, "\n")
        })
      }
    }
  }
}

# 加载核心包
cat("加载核心包...\n")
suppressPackageStartupMessages({
  safe_require("shiny")
  safe_require("shinydashboard")
  if (requireNamespace("shinyWidgets", quietly = TRUE)) safe_require("shinyWidgets")
  if (requireNamespace("DT", quietly = TRUE)) safe_require("DT")
  if (requireNamespace("dplyr", quietly = TRUE)) safe_require("dplyr")
  if (requireNamespace("ggplot2", quietly = TRUE)) safe_require("ggplot2")
})

cat("✓ 核心包加载完成\n\n")

# 检查系统文件
cat("检查系统文件...\n")
required_files <- c(
  "app.R",
  "global.R",
  "ui/ui_main.R",
  "ui/ui_dashboard.R",
  "ui/ui_data.R",
  "ui/ui_analysis.R",
  "ui/ui_results.R",
  "ui/ui_reports.R",
  "ui/ui_settings.R",
  "server/server_main.R",
  "modules/data_processing.R",
  "modules/statistical_analysis.R",
  "modules/visualization.R",
  "utils/helpers.R"
)

missing_files <- c()
for (file in required_files) {
  if (!file.exists(file)) {
    missing_files <- c(missing_files, file)
  }
}

if (length(missing_files) > 0) {
  cat("缺少以下系统文件:\n")
  for (file in missing_files) {
    cat("  -", file, "\n")
  }
  cat("\n将使用简化版本启动...\n")
  source("quick_start.R")
  return()
}

cat("✓ 系统文件检查完成\n\n")

# 创建必要的目录
cat("创建系统目录...\n")
required_dirs <- c("data", "reports", "logs", "temp", "exports", "www", "www/css", "www/js")
for (dir in required_dirs) {
  if (!dir.exists(dir)) {
    dir.create(dir, recursive = TRUE, showWarnings = FALSE)
    cat("创建目录:", dir, "\n")
  }
}

# 生成示例数据
if (!file.exists("data/sample_data.csv")) {
  cat("生成示例数据...\n")
  
  set.seed(123)
  n <- 500
  
  sample_data <- data.frame(
    patient_id = 1:n,
    age = pmax(18, pmin(100, round(rnorm(n, 65, 15)))),
    gender = sample(c("Male", "Female"), n, replace = TRUE, prob = c(0.55, 0.45)),
    creatinine = pmax(0.5, pmin(5.0, round(rnorm(n, 1.2, 0.4), 2))),
    bun = pmax(5, pmin(80, round(rnorm(n, 20, 8), 1))),
    wbc = pmax(2, pmin(25, round(rnorm(n, 8.5, 3.2), 1))),
    hemoglobin = pmax(6, pmin(18, round(rnorm(n, 12, 2.5), 1))),
    heart_rate = pmax(40, pmin(180, round(rnorm(n, 85, 20)))),
    systolic_bp = pmax(80, pmin(220, round(rnorm(n, 130, 25)))),
    respiratory_rate = pmax(8, pmin(40, round(rnorm(n, 18, 5)))),
    sofa_score = pmax(0, pmin(20, round(rnorm(n, 6, 3)))),
    apache_score = pmax(0, pmin(50, round(rnorm(n, 15, 8)))),
    diabetes = sample(c(0, 1), n, replace = TRUE, prob = c(0.7, 0.3)),
    hypertension = sample(c(0, 1), n, replace = TRUE, prob = c(0.6, 0.4)),
    heart_disease = sample(c(0, 1), n, replace = TRUE, prob = c(0.75, 0.25)),
    kidney_disease = sample(c(0, 1), n, replace = TRUE, prob = c(0.8, 0.2)),
    los_icu = pmax(1, pmin(30, round(rexp(n, 0.2)))),
    stringsAsFactors = FALSE
  )
  
  # 生成结局变量
  death_prob <- plogis(-2.5 + 
                      0.05 * (sample_data$age - 65) + 
                      0.3 * (sample_data$gender == "Male") +
                      0.2 * sample_data$sofa_score +
                      0.1 * sample_data$apache_score +
                      0.5 * sample_data$diabetes +
                      0.3 * sample_data$heart_disease +
                      0.4 * sample_data$kidney_disease +
                      0.1 * (sample_data$creatinine - 1.2))
  
  sample_data$death_28d <- rbinom(n, 1, death_prob)
  
  # 添加缺失值
  missing_indices <- sample(1:n, n * 0.05)
  sample_data$creatinine[sample(missing_indices, length(missing_indices) * 0.3)] <- NA
  sample_data$bun[sample(missing_indices, length(missing_indices) * 0.2)] <- NA
  sample_data$hemoglobin[sample(missing_indices, length(missing_indices) * 0.1)] <- NA
  
  write.csv(sample_data, "data/sample_data.csv", row.names = FALSE)
  cat("✓ 示例数据已生成\n")
}

cat("\n系统准备完成！\n")
cat("System ready!\n\n")

# 启动主应用
cat("启动完整版医学数据分析系统...\n")
cat("Starting full Medical Data Analysis System...\n\n")

cat("系统特色:\n")
cat("System Features:\n")
cat("✓ 完整的数据预处理流程\n")
cat("✓ 多种统计分析方法\n")
cat("✓ 专业的医学统计图表\n")
cat("✓ 预测模型构建和评估\n")
cat("✓ 自动化报告生成\n")
cat("✓ 交互式结果展示\n\n")

cat("访问地址: http://localhost:3838\n")
cat("Access URL: http://localhost:3838\n")
cat("按 Ctrl+C 停止应用\n\n")

# 启动应用
tryCatch({
  source("app.R")
}, error = function(e) {
  cat("主应用启动失败:", e$message, "\n")
  cat("Main application failed:", e$message, "\n\n")
  
  cat("错误详情:\n")
  cat("Error details:\n")
  print(e)
  
  cat("\n尝试启动简化版本...\n")
  cat("Trying simplified version...\n")
  
  # 回退到简化版本
  source("quick_start.R")
})
