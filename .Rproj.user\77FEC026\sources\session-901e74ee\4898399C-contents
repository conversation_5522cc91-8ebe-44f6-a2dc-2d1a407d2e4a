# 医学数据分析系统 - 简化启动脚本
# Medical Data Analysis System - Simplified Launch Script

cat("=================================================\n")
cat("医学数据分析系统\n")
cat("Medical Data Analysis System\n")
cat("=================================================\n\n")

# 检查工作目录
if (!file.exists("global.R")) {
  if (file.exists("medical_analysis_system/global.R")) {
    setwd("medical_analysis_system")
    cat("切换到系统目录\n")
  } else {
    stop("错误：找不到系统文件。请确保在正确的目录中运行此脚本。")
  }
}

cat("当前工作目录:", getwd(), "\n")
cat("R版本:", R.version.string, "\n\n")

# 检查必要的包
cat("检查必要的R包...\n")
required_packages <- c(
  "shiny", "shinydashboard", "shinyWidgets", "DT", 
  "dplyr", "ggplot2", "plotly"
)

missing_packages <- c()
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    missing_packages <- c(missing_packages, pkg)
  }
}

if (length(missing_packages) > 0) {
  cat("缺少以下R包:\n")
  for (pkg in missing_packages) {
    cat("  -", pkg, "\n")
  }
  
  cat("\n正在尝试安装缺少的包...\n")
  for (pkg in missing_packages) {
    cat("安装", pkg, "...\n")
    tryCatch({
      install.packages(pkg, dependencies = TRUE)
      cat("✓", pkg, "安装成功\n")
    }, error = function(e) {
      cat("✗", pkg, "安装失败:", e$message, "\n")
    })
  }
  cat("\n")
}

# 加载必要的包
cat("加载系统包...\n")
suppressPackageStartupMessages({
  library(shiny)
  library(shinydashboard)
  if (requireNamespace("shinyWidgets", quietly = TRUE)) library(shinyWidgets)
  if (requireNamespace("DT", quietly = TRUE)) library(DT)
  if (requireNamespace("dplyr", quietly = TRUE)) library(dplyr)
  if (requireNamespace("ggplot2", quietly = TRUE)) library(ggplot2)
  if (requireNamespace("plotly", quietly = TRUE)) library(plotly)
})

cat("✓ 基础包加载完成\n\n")

# 创建必要的目录
cat("检查系统目录...\n")
required_dirs <- c("data", "reports", "logs", "temp", "exports", "www", "www/css")
for (dir in required_dirs) {
  if (!dir.exists(dir)) {
    dir.create(dir, recursive = TRUE)
    cat("创建目录:", dir, "\n")
  }
}

# 生成示例数据
if (!file.exists("data/sample_data.csv")) {
  cat("生成示例数据...\n")
  
  set.seed(123)
  n <- 500
  
  sample_data <- data.frame(
    patient_id = 1:n,
    age = pmax(18, pmin(100, round(rnorm(n, 65, 15)))),
    gender = sample(c("Male", "Female"), n, replace = TRUE, prob = c(0.55, 0.45)),
    creatinine = pmax(0.5, pmin(5.0, round(rnorm(n, 1.2, 0.4), 2))),
    bun = pmax(5, pmin(80, round(rnorm(n, 20, 8), 1))),
    wbc = pmax(2, pmin(25, round(rnorm(n, 8.5, 3.2), 1))),
    heart_rate = pmax(40, pmin(180, round(rnorm(n, 85, 20)))),
    systolic_bp = pmax(80, pmin(220, round(rnorm(n, 130, 25)))),
    sofa_score = pmax(0, pmin(20, round(rnorm(n, 6, 3)))),
    apache_score = pmax(0, pmin(50, round(rnorm(n, 15, 8)))),
    diabetes = sample(c(0, 1), n, replace = TRUE, prob = c(0.7, 0.3)),
    hypertension = sample(c(0, 1), n, replace = TRUE, prob = c(0.6, 0.4)),
    los_icu = pmax(1, pmin(30, round(rexp(n, 0.2)))),
    stringsAsFactors = FALSE
  )
  
  # 生成结局变量
  death_prob <- plogis(-2.5 + 
                      0.05 * (sample_data$age - 65) + 
                      0.3 * (sample_data$gender == "Male") +
                      0.2 * sample_data$sofa_score +
                      0.5 * sample_data$diabetes)
  
  sample_data$death_28d <- rbinom(n, 1, death_prob)
  
  # 添加缺失值
  missing_indices <- sample(1:n, n * 0.05)
  sample_data$creatinine[sample(missing_indices, length(missing_indices) * 0.3)] <- NA
  sample_data$bun[sample(missing_indices, length(missing_indices) * 0.2)] <- NA
  
  write.csv(sample_data, "data/sample_data.csv", row.names = FALSE)
  cat("✓ 示例数据已生成\n")
}

cat("\n系统准备完成！\n")
cat("System ready!\n\n")

cat("功能特色:\n")
cat("Features:\n")
cat("✓ 自动化数据预处理\n")
cat("✓ 完整的统计分析流程\n")
cat("✓ 专业的医学统计图表\n")
cat("✓ 交互式结果展示\n\n")

cat("启动Web应用...\n")
cat("Starting web application...\n")
cat("访问地址: http://localhost:3838\n")
cat("Access URL: http://localhost:3838\n")
cat("按 Ctrl+C 停止应用\n\n")

# 启动应用
tryCatch({
  # 设置选项
  options(
    shiny.host = "0.0.0.0",
    shiny.port = 3838,
    shiny.launch.browser = TRUE,
    shiny.maxRequestSize = 100*1024^2
  )
  
  # 运行应用
  shiny::runApp(
    appDir = ".",
    host = "0.0.0.0",
    port = 3838,
    launch.browser = TRUE
  )
  
}, error = function(e) {
  cat("应用启动失败:", e$message, "\n")
  cat("Application failed to start:", e$message, "\n")
  
  # 尝试其他端口
  alternative_ports <- c(8080, 8888, 9999)
  for (port in alternative_ports) {
    cat("尝试端口", port, "...\n")
    tryCatch({
      shiny::runApp(
        appDir = ".",
        host = "0.0.0.0",
        port = port,
        launch.browser = TRUE
      )
      break
    }, error = function(e2) {
      cat("端口", port, "失败\n")
    })
  }
}, finally = {
  cat("\n应用已停止\n")
  cat("Application stopped\n")
})
