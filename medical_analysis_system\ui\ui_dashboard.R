# 医学数据分析系统 - 仪表板界面
# Medical Data Analysis System - Dashboard UI

ui_dashboard <- fluidPage(
  # 页面标题
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                 border-radius: 10px; color: white; text-align: center;",
        h1(
          icon("tachometer-alt", style = "margin-right: 15px;"),
          "医学数据分析系统仪表板",
          style = "margin: 0; font-size: 28px; font-weight: 300;"
        ),
        p(
          "Medical Data Analysis System Dashboard",
          style = "margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;"
        )
      )
    )
  ),
  
  # 系统状态卡片
  fluidRow(
    # 数据状态
    column(3,
      valueBoxOutput("data_status_box", width = NULL)
    ),
    
    # 分析任务
    column(3,
      valueBoxOutput("analysis_tasks_box", width = NULL)
    ),
    
    # 生成报告
    column(3,
      valueBoxOutput("reports_count_box", width = NULL)
    ),
    
    # 系统运行时间
    column(3,
      valueBoxOutput("system_uptime_box", width = NULL)
    )
  ),
  
  # 主要功能区域
  fluidRow(
    # 快速开始
    column(6,
      box(
        title = tagList(icon("rocket"), " 快速开始"),
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        height = "400px",
        
        div(
          style = "padding: 20px;",
          
          h4("开始您的数据分析之旅", style = "color: #2c3e50; margin-bottom: 20px;"),
          
          div(
            class = "quick-start-item",
            style = "margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db;",
            div(
              style = "display: flex; align-items: center;",
              icon("upload", style = "font-size: 20px; color: #3498db; margin-right: 15px;"),
              div(
                h5("1. 上传数据", style = "margin: 0; color: #2c3e50;"),
                p("支持CSV、Excel等格式，最大100MB", style = "margin: 5px 0 0 0; color: #7f8c8d; font-size: 14px;")
              )
            )
          ),
          
          div(
            class = "quick-start-item",
            style = "margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #2ecc71;",
            div(
              style = "display: flex; align-items: center;",
              icon("cogs", style = "font-size: 20px; color: #2ecc71; margin-right: 15px;"),
              div(
                h5("2. 配置分析", style = "margin: 0; color: #2c3e50;"),
                p("选择变量和分析方法", style = "margin: 5px 0 0 0; color: #7f8c8d; font-size: 14px;")
              )
            )
          ),
          
          div(
            class = "quick-start-item",
            style = "margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #f39c12;",
            div(
              style = "display: flex; align-items: center;",
              icon("play", style = "font-size: 20px; color: #f39c12; margin-right: 15px;"),
              div(
                h5("3. 执行分析", style = "margin: 0; color: #2c3e50;"),
                p("一键启动完整分析流程", style = "margin: 5px 0 0 0; color: #7f8c8d; font-size: 14px;")
              )
            )
          ),
          
          div(
            class = "quick-start-item",
            style = "margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #e74c3c;",
            div(
              style = "display: flex; align-items: center;",
              icon("chart-bar", style = "font-size: 20px; color: #e74c3c; margin-right: 15px;"),
              div(
                h5("4. 查看结果", style = "margin: 0; color: #2c3e50;"),
                p("交互式图表和专业报告", style = "margin: 5px 0 0 0; color: #7f8c8d; font-size: 14px;")
              )
            )
          ),
          
          div(
            style = "text-align: center;",
            actionButton(
              "start_analysis",
              "开始分析",
              icon = icon("play"),
              class = "btn-primary btn-lg",
              style = "padding: 12px 30px; font-size: 16px; border-radius: 25px;"
            )
          )
        )
      )
    ),
    
    # 系统信息
    column(6,
      box(
        title = tagList(icon("info-circle"), " 系统信息"),
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        height = "400px",
        
        div(
          style = "padding: 20px;",
          
          # 功能特色
          h4("功能特色", style = "color: #2c3e50; margin-bottom: 15px;"),
          
          tags$ul(
            style = "list-style: none; padding: 0;",
            
            tags$li(
              style = "margin-bottom: 10px; padding: 8px; background: #e8f5e8; border-radius: 5px;",
              icon("check", style = "color: #27ae60; margin-right: 10px;"),
              "自动化数据预处理和清洗"
            ),
            
            tags$li(
              style = "margin-bottom: 10px; padding: 8px; background: #e8f4fd; border-radius: 5px;",
              icon("check", style = "color: #3498db; margin-right: 10px;"),
              "完整的统计分析流程"
            ),
            
            tags$li(
              style = "margin-bottom: 10px; padding: 8px; background: #fff3cd; border-radius: 5px;",
              icon("check", style = "color: #f39c12; margin-right: 10px;"),
              "专业的医学统计图表"
            ),
            
            tags$li(
              style = "margin-bottom: 10px; padding: 8px; background: #f8d7da; border-radius: 5px;",
              icon("check", style = "color: #e74c3c; margin-right: 10px;"),
              "一键生成分析报告"
            ),
            
            tags$li(
              style = "margin-bottom: 15px; padding: 8px; background: #e2e3e5; border-radius: 5px;",
              icon("check", style = "color: #6c757d; margin-right: 10px;"),
              "交互式结果展示"
            )
          ),
          
          # 技术支持
          div(
            style = "margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;",
            h5("技术支持", style = "margin: 0 0 10px 0; color: #495057;"),
            p(
              "基于R语言和Shiny框架开发，支持MIMIC数据库等医学数据的专业分析。",
              style = "margin: 0; color: #6c757d; font-size: 14px; line-height: 1.5;"
            )
          )
        )
      )
    )
  ),
  
  # 最近活动
  fluidRow(
    column(12,
      box(
        title = tagList(icon("history"), " 最近活动"),
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            id = "recent_activities",
            style = "min-height: 200px;",
            
            div(
              style = "text-align: center; padding: 50px; color: #6c757d;",
              icon("clock", style = "font-size: 48px; margin-bottom: 15px; opacity: 0.5;"),
              h4("暂无活动记录", style = "margin: 0; font-weight: 300;"),
              p("开始您的第一次数据分析", style = "margin: 10px 0 0 0; font-size: 14px;")
            )
          )
        )
      )
    )
  )
)
