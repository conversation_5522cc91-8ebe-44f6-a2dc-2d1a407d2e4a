# 医学数据分析系统 - 简化启动脚本
# Medical Data Analysis System - Simple Start Script

cat("=================================================\n")
cat("医学数据分析系统 - 简化版本\n")
cat("Medical Data Analysis System - Simplified Version\n")
cat("=================================================\n\n")

# 检查工作目录
if (!file.exists("global.R")) {
  if (file.exists("medical_analysis_system/global.R")) {
    setwd("medical_analysis_system")
  } else {
    stop("请在medical_analysis_system目录中运行此脚本")
  }
}

# 加载必要的包
suppressPackageStartupMessages({
  library(shiny)
  library(shinydashboard)
  if (requireNamespace("DT", quietly = TRUE)) library(DT)
  if (requireNamespace("dplyr", quietly = TRUE)) library(dplyr)
  if (requireNamespace("ggplot2", quietly = TRUE)) library(ggplot2)
})

# 创建目录
if (!dir.exists("data")) dir.create("data")
if (!dir.exists("www")) dir.create("www")

# 生成示例数据
if (!file.exists("data/sample_data.csv")) {
  set.seed(123)
  n <- 500
  
  sample_data <- data.frame(
    patient_id = 1:n,
    age = pmax(18, pmin(100, round(rnorm(n, 65, 15)))),
    gender = sample(c("Male", "Female"), n, replace = TRUE),
    creatinine = pmax(0.5, round(rnorm(n, 1.2, 0.4), 2)),
    sofa_score = pmax(0, round(rnorm(n, 6, 3))),
    diabetes = sample(c(0, 1), n, replace = TRUE, prob = c(0.7, 0.3)),
    death_28d = sample(c(0, 1), n, replace = TRUE, prob = c(0.8, 0.2)),
    stringsAsFactors = FALSE
  )
  
  write.csv(sample_data, "data/sample_data.csv", row.names = FALSE)
  cat("✓ 示例数据已生成\n")
}

# 简化的UI
ui <- dashboardPage(
  skin = "blue",
  
  dashboardHeader(
    title = "医学数据分析系统"
  ),
  
  dashboardSidebar(
    sidebarMenu(
      menuItem("仪表板", tabName = "dashboard", icon = icon("tachometer-alt")),
      menuItem("数据上传", tabName = "upload", icon = icon("upload")),
      menuItem("数据预览", tabName = "preview", icon = icon("table")),
      menuItem("统计分析", tabName = "analysis", icon = icon("chart-line"))
    )
  ),
  
  dashboardBody(
    tabItems(
      # 仪表板
      tabItem(
        tabName = "dashboard",
        fluidRow(
          column(12,
            h2("欢迎使用医学数据分析系统"),
            p("这是一个基于Shiny的医学数据统计分析系统，支持数据上传、预处理、统计分析和结果可视化。")
          )
        ),
        fluidRow(
          valueBoxOutput("total_patients"),
          valueBoxOutput("avg_age"),
          valueBoxOutput("death_rate")
        )
      ),
      
      # 数据上传
      tabItem(
        tabName = "upload",
        fluidRow(
          column(6,
            box(
              title = "数据上传",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              
              fileInput("file", "选择数据文件",
                       accept = c(".csv", ".xlsx", ".xls")),
              
              checkboxInput("header", "包含列名", TRUE),
              
              radioButtons("sep", "分隔符",
                          choices = c(逗号 = ",", 分号 = ";", 制表符 = "\t"),
                          selected = ","),
              
              actionButton("load_data", "加载数据", class = "btn-primary")
            )
          ),
          column(6,
            box(
              title = "示例数据",
              status = "info",
              solidHeader = TRUE,
              width = NULL,
              
              p("您可以使用系统内置的示例数据进行测试："),
              actionButton("load_sample", "加载示例数据", class = "btn-info"),
              
              br(), br(),
              p("示例数据包含500例患者的基本信息、生化指标和28天死亡率等变量。")
            )
          )
        )
      ),
      
      # 数据预览
      tabItem(
        tabName = "preview",
        fluidRow(
          column(12,
            box(
              title = "数据预览",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.data_loaded",
                DT::dataTableOutput("data_table")
              ),
              
              conditionalPanel(
                condition = "!output.data_loaded",
                div(
                  style = "text-align: center; padding: 50px;",
                  h4("请先上传数据文件"),
                  p("在数据上传页面选择文件或加载示例数据")
                )
              )
            )
          )
        )
      ),
      
      # 统计分析
      tabItem(
        tabName = "analysis",
        fluidRow(
          column(4,
            box(
              title = "分析设置",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.data_loaded",
                selectInput("outcome_var", "结局变量", choices = NULL),
                checkboxGroupInput("covariates", "协变量", choices = NULL),
                actionButton("run_analysis", "开始分析", class = "btn-success")
              ),
              
              conditionalPanel(
                condition = "!output.data_loaded",
                p("请先上传数据")
              )
            )
          ),
          column(8,
            box(
              title = "分析结果",
              status = "success",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.analysis_done",
                DT::dataTableOutput("analysis_results")
              ),
              
              conditionalPanel(
                condition = "!output.analysis_done",
                div(
                  style = "text-align: center; padding: 50px;",
                  h4("等待分析"),
                  p("请配置分析参数并点击开始分析")
                )
              )
            )
          )
        )
      )
    )
  )
)

# 简化的服务器逻辑
server <- function(input, output, session) {
  
  # 响应式数据
  values <- reactiveValues(
    data = NULL,
    analysis_results = NULL
  )
  
  # 加载示例数据
  observeEvent(input$load_sample, {
    values$data <- read.csv("data/sample_data.csv", stringsAsFactors = FALSE)
    showNotification("示例数据加载成功！", type = "message")
    
    # 更新变量选择
    binary_vars <- names(values$data)[sapply(values$data, function(x) length(unique(x)) == 2)]
    all_vars <- names(values$data)
    
    updateSelectInput(session, "outcome_var", choices = binary_vars)
    updateCheckboxGroupInput(session, "covariates", choices = all_vars)
  })
  
  # 文件上传
  observeEvent(input$load_data, {
    req(input$file)
    
    tryCatch({
      if (tools::file_ext(input$file$name) == "csv") {
        values$data <- read.csv(input$file$datapath, 
                               header = input$header,
                               sep = input$sep,
                               stringsAsFactors = FALSE)
      } else {
        values$data <- readxl::read_excel(input$file$datapath)
      }
      
      showNotification("数据上传成功！", type = "message")
      
      # 更新变量选择
      binary_vars <- names(values$data)[sapply(values$data, function(x) length(unique(x)) == 2)]
      all_vars <- names(values$data)
      
      updateSelectInput(session, "outcome_var", choices = binary_vars)
      updateCheckboxGroupInput(session, "covariates", choices = all_vars)
      
    }, error = function(e) {
      showNotification(paste("数据上传失败:", e$message), type = "error")
    })
  })
  
  # 数据是否已加载
  output$data_loaded <- reactive({
    !is.null(values$data)
  })
  outputOptions(output, "data_loaded", suspendWhenHidden = FALSE)
  
  # 分析是否完成
  output$analysis_done <- reactive({
    !is.null(values$analysis_results)
  })
  outputOptions(output, "analysis_done", suspendWhenHidden = FALSE)
  
  # 值框
  output$total_patients <- renderValueBox({
    valueBox(
      value = if (is.null(values$data)) 0 else nrow(values$data),
      subtitle = "总患者数",
      icon = icon("users"),
      color = "blue"
    )
  })
  
  output$avg_age <- renderValueBox({
    avg_age <- if (is.null(values$data) || !"age" %in% names(values$data)) {
      "N/A"
    } else {
      round(mean(values$data$age, na.rm = TRUE), 1)
    }
    
    valueBox(
      value = avg_age,
      subtitle = "平均年龄",
      icon = icon("birthday-cake"),
      color = "green"
    )
  })
  
  output$death_rate <- renderValueBox({
    death_rate <- if (is.null(values$data) || !"death_28d" %in% names(values$data)) {
      "N/A"
    } else {
      paste0(round(mean(values$data$death_28d, na.rm = TRUE) * 100, 1), "%")
    }
    
    valueBox(
      value = death_rate,
      subtitle = "28天死亡率",
      icon = icon("heartbeat"),
      color = "red"
    )
  })
  
  # 数据表格
  output$data_table <- DT::renderDataTable({
    req(values$data)
    DT::datatable(values$data, options = list(scrollX = TRUE))
  })
  
  # 统计分析
  observeEvent(input$run_analysis, {
    req(values$data, input$outcome_var, input$covariates)
    
    tryCatch({
      results <- data.frame(
        Variable = character(),
        OR = numeric(),
        P_Value = numeric(),
        stringsAsFactors = FALSE
      )
      
      for (var in input$covariates) {
        if (var != input$outcome_var) {
          formula_str <- paste(input$outcome_var, "~", var)
          model <- glm(as.formula(formula_str), data = values$data, family = binomial())
          
          coef_summary <- summary(model)$coefficients
          if (nrow(coef_summary) > 1) {
            or <- exp(coef_summary[2, 1])
            p_value <- coef_summary[2, 4]
            
            results <- rbind(results, data.frame(
              Variable = var,
              OR = round(or, 3),
              P_Value = round(p_value, 4)
            ))
          }
        }
      }
      
      values$analysis_results <- results
      showNotification("分析完成！", type = "message")
      
    }, error = function(e) {
      showNotification(paste("分析失败:", e$message), type = "error")
    })
  })
  
  # 分析结果表格
  output$analysis_results <- DT::renderDataTable({
    req(values$analysis_results)
    DT::datatable(values$analysis_results, options = list(pageLength = 10))
  })
}

# 启动应用
cat("启动简化版医学数据分析系统...\n")
cat("访问地址: http://localhost:3838\n\n")

shinyApp(ui = ui, server = server)
