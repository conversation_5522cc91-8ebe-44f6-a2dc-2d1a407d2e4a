# 医学数据分析系统 - 主应用入口
# Medical Data Analysis System - Main Application Entry

cat("启动医学数据分析系统...\n")
cat("Starting Medical Data Analysis System...\n\n")

# 安全加载文件的函数
safe_source <- function(file_path, description = "") {
  if (file.exists(file_path)) {
    tryCatch({
      source(file_path)
      cat("✓ 已加载:", description, "\n")
      return(TRUE)
    }, error = function(e) {
      cat("✗ 加载失败:", description, "-", e$message, "\n")
      return(FALSE)
    })
  } else {
    cat("✗ 文件不存在:", file_path, "\n")
    return(FALSE)
  }
}

# 1. 加载全局配置
cat("加载系统配置...\n")
if (!safe_source("global.R", "全局配置")) {
  stop("全局配置加载失败，无法启动系统")
}

# 2. 加载工具函数
cat("加载工具函数...\n")
safe_source("utils/helpers.R", "辅助函数")
safe_source("utils/validators.R", "数据验证")
safe_source("utils/config.R", "配置管理")

# 3. 加载核心分析模块
cat("加载分析模块...\n")
safe_source("modules/data_processing.R", "数据处理模块")
safe_source("modules/statistical_analysis.R", "统计分析模块")
safe_source("modules/visualization.R", "可视化模块")
safe_source("modules/model_building.R", "模型构建模块")

# 4. 加载UI模块
cat("加载界面模块...\n")
ui_loaded <- TRUE
ui_loaded <- ui_loaded && safe_source("ui/ui_dashboard.R", "仪表板界面")
ui_loaded <- ui_loaded && safe_source("ui/ui_data.R", "数据管理界面")
ui_loaded <- ui_loaded && safe_source("ui/ui_analysis.R", "分析配置界面")
ui_loaded <- ui_loaded && safe_source("ui/ui_results.R", "结果展示界面")
ui_loaded <- ui_loaded && safe_source("ui/ui_reports.R", "报告中心界面")
ui_loaded <- ui_loaded && safe_source("ui/ui_settings.R", "系统设置界面")

if (ui_loaded) {
  safe_source("ui/ui_main.R", "主界面")
} else {
  cat("部分UI模块加载失败，使用简化界面\n")
  # 使用简化的UI
  source("quick_start.R", local = TRUE)
  return()
}

# 5. 加载服务器模块
cat("加载服务器逻辑...\n")
if (!safe_source("server/server_main.R", "主服务器逻辑")) {
  cat("服务器逻辑加载失败，使用简化版本\n")
  source("quick_start.R", local = TRUE)
  return()
}

cat("\n系统组件加载完成！\n")
cat("System components loaded successfully!\n\n")

# 创建Shiny应用
cat("启动Web应用...\n")
cat("Starting web application...\n")
cat("访问地址: http://localhost:3838\n")
cat("Access URL: http://localhost:3838\n\n")

tryCatch({
  shinyApp(
    ui = ui,
    server = server,
    options = list(
      host = "0.0.0.0",
      port = 3838,
      launch.browser = TRUE
    )
  )
}, error = function(e) {
  cat("主应用启动失败:", e$message, "\n")
  cat("Main application failed to start:", e$message, "\n")
  cat("尝试启动简化版本...\n")
  cat("Trying to start simplified version...\n")

  # 回退到简化版本
  source("quick_start.R", local = TRUE)
})
