# 医学数据分析系统 - 全局配置
# Medical Data Analysis System - Global Configuration

# 安全加载必要的包
safe_library <- function(pkg) {
  if (requireNamespace(pkg, quietly = TRUE)) {
    library(pkg, character.only = TRUE)
    return(TRUE)
  } else {
    cat("警告: 包", pkg, "未安装\n")
    return(FALSE)
  }
}

# 核心必需包
suppressPackageStartupMessages({
  # Shiny核心
  library(shiny)
  library(shinydashboard)
  safe_library("shinyWidgets")
  safe_library("DT")
  safe_library("plotly")
  safe_library("shinyjs")

  # 数据处理核心
  safe_library("dplyr")
  safe_library("tidyr")
  safe_library("stringr")
  safe_library("readr")

  # 统计分析核心
  safe_library("survival")
  safe_library("rms")
  safe_library("glmnet")
  safe_library("pROC")
  safe_library("broom")

  # 可视化核心
  library(ggplot2)
  safe_library("corrplot")
  safe_library("RColorBrewer")

  # 工具包
  safe_library("jsonlite")
})

# 检查关键包是否可用
key_packages_available <- list(
  mice = requireNamespace("mice", quietly = TRUE),
  VIM = requireNamespace("VIM", quietly = TRUE),
  forestplot = requireNamespace("forestplot", quietly = TRUE),
  tableone = requireNamespace("tableone", quietly = TRUE)
)

# 设置全局选项
options(
  shiny.maxRequestSize = 100*1024^2,  # 最大上传文件大小100MB
  digits = 4,                         # 数字显示精度
  scipen = 999,                       # 避免科学计数法
  stringsAsFactors = FALSE,           # 字符串不自动转因子
  warn = -1                           # 抑制警告信息
)

# 创建必要的目录
dir_create <- function(path) {
  if (!dir.exists(path)) {
    dir.create(path, recursive = TRUE)
  }
}

# 创建目录结构
dir_create("data")
dir_create("reports")
dir_create("logs")
dir_create("temp")
dir_create("exports")

# 简化的日志函数
log_info <- function(msg) {
  cat("[INFO]", Sys.time(), "-", msg, "\n")
}

log_warn <- function(msg) {
  cat("[WARN]", Sys.time(), "-", msg, "\n")
}

log_error <- function(msg) {
  cat("[ERROR]", Sys.time(), "-", msg, "\n")
}

# 全局变量
SUPPORTED_FILE_TYPES <- c(".csv", ".tsv", ".txt", ".xlsx", ".xls")
MAX_FILE_SIZE_MB <- 100
DEFAULT_SEED <- 123

# 应用配置
APP_CONFIG <- list(
  name = "医学数据分析系统",
  version = "1.0.0",
  author = "Medical Data Analysis Team",
  description = "基于Shiny的医学数据统计分析和预测建模系统",
  
  # 界面配置
  ui = list(
    theme = "flatly",
    sidebar_width = 250,
    main_color = "#3498db",
    success_color = "#2ecc71",
    warning_color = "#f39c12",
    danger_color = "#e74c3c"
  ),
  
  # 分析配置
  analysis = list(
    default_test_size = 0.3,
    default_cv_folds = 5,
    lasso_alpha = 1,
    max_iterations = 1000,
    convergence_threshold = 1e-6
  ),
  
  # 可视化配置
  plots = list(
    width = 800,
    height = 600,
    dpi = 300,
    format = "png",
    theme = "minimal"
  )
)

# 错误处理函数
handle_error <- function(error, context = "Unknown") {
  error_msg <- paste("Error in", context, ":", conditionMessage(error))
  log_error(error_msg)
  showNotification(
    paste("发生错误:", conditionMessage(error)),
    type = "error",
    duration = 10
  )
  return(NULL)
}

# 成功消息函数
show_success <- function(message, duration = 3) {
  showNotification(
    message,
    type = "message",
    duration = duration
  )
  log_info(message)
}

# 警告消息函数
show_warning <- function(message, duration = 5) {
  showNotification(
    message,
    type = "warning", 
    duration = duration
  )
  log_warn(message)
}

# 数据验证函数
validate_data <- function(data) {
  if (is.null(data) || nrow(data) == 0) {
    return(list(valid = FALSE, message = "数据为空"))
  }
  
  if (ncol(data) < 2) {
    return(list(valid = FALSE, message = "数据列数不足"))
  }
  
  return(list(valid = TRUE, message = "数据验证通过"))
}

# 格式化数字函数
format_number <- function(x, digits = 3) {
  if (is.numeric(x)) {
    return(round(x, digits))
  }
  return(x)
}

# 格式化p值函数
format_pvalue <- function(p, threshold = 0.001) {
  if (is.numeric(p)) {
    if (p < threshold) {
      return(paste0("< ", threshold))
    } else {
      return(sprintf("%.3f", p))
    }
  }
  return(p)
}

# 生成唯一ID函数
generate_id <- function(prefix = "analysis") {
  paste0(prefix, "_", format(Sys.time(), "%Y%m%d_%H%M%S"), "_", 
         sample(1000:9999, 1))
}

# 保存分析结果函数
save_analysis_results <- function(results, id) {
  file_path <- file.path("exports", paste0(id, ".rds"))
  saveRDS(results, file_path)
  log_info(paste("Analysis results saved:", file_path))
  return(file_path)
}

# 加载分析结果函数
load_analysis_results <- function(id) {
  file_path <- file.path("exports", paste0(id, ".rds"))
  if (file.exists(file_path)) {
    return(readRDS(file_path))
  }
  return(NULL)
}

# 清理临时文件函数
cleanup_temp_files <- function() {
  temp_files <- list.files("temp", full.names = TRUE)
  if (length(temp_files) > 0) {
    file.remove(temp_files)
    log_info(paste("Cleaned up", length(temp_files), "temporary files"))
  }
}

# 应用启动时的初始化
tryCatch({
  log_info("Medical Data Analysis System starting...")
  log_info(paste("R version:", R.version.string))
  log_info(paste("Shiny version:", packageVersion("shiny")))

  # 清理旧的临时文件
  if (exists("cleanup_temp_files")) {
    cleanup_temp_files()
  }
}, error = function(e) {
  cat("初始化警告:", e$message, "\n")
})

cat("全局配置加载完成 | Global configuration loaded successfully\n")
